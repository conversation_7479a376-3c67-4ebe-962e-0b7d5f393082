#!/bin/bash

# Проверка на уже запущенный процесс
PID_FILE="/tmp/restore-script.pid"
if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if ps -p $PID > /dev/null; then
        echo "Процесс восстановления уже запущен (PID: $PID)"
        exit 1
    fi
fi

# Записываем PID текущего процесса
echo $$ > "$PID_FILE"

# Имя хоста для отображения в сообщениях
BAKHOST="sakhalife.ru"

# Настройки Telegram
TELEGRAM_BOT_TOKEN="**********************************************"
TELEGRAM_CHAT_ID="-1002644405949"
PROGRESS_MESSAGE_ID=""

# Функция для расчета оставшегося времени
function estimate_remaining() {
    local elapsed="$1"  # Прошедшее время в секундах
    local progress="$2"  # Текущий прогресс в процентах

    # Если прогресс меньше 5%, недостаточно данных для точного расчета
    if [[ $progress -le 5 ]]; then
        echo "расчет..."
    else
        # Избегаем деления на ноль
        if [ $progress -eq 0 ]; then
            echo "расчет..."
        else
            # Рассчитываем общее предполагаемое время и оставшееся время
            local total_estimated=$(awk "BEGIN {printf \"%.0f\", $elapsed*100/$progress}")
            local remaining=$((total_estimated-elapsed))

            # Проверяем, что оставшееся время не отрицательное
            if [ $remaining -lt 0 ]; then
                remaining=0
            fi

            # Форматируем время в формате MM:SS (минуты:секунды)
            printf "%02d:%02d" $((remaining/60)) $((remaining%60))
        fi
    fi
}

# Функция для обновления прогресса в Telegram
function update_progress() {
    local progress="$1"  # Прогресс в процентах (0-100)
    local status="$2"    # Статус операции (например, "Процесс", "Завершен")
    local phase="$3"     # Фаза операции (например, "DB Backup", "Очистка снапшотов")
    local remaining="$4" # Оставшееся время в формате MM:SS

    # Создаем прогресс-бар из синих блоков (10 символов)
    local filled=$((progress/10))  # Количество заполненных блоков
    local empty=$((10-filled))    # Количество пустых блоков
    local bar=""
    for ((i=0; i<filled; i++)); do
        bar="${bar}🟦"  # Синий квадрат
    done
    for ((i=0; i<empty; i++)); do
        bar="${bar}⬜"  # Белый квадрат
    done

    # Формируем сообщение с переносами строк
    local full_message="${BAKHOST}
${bar} ${progress}% 🕘 ${remaining}
${phase}: ${status}"

    if [[ -z "$PROGRESS_MESSAGE_ID" ]]; then
        PROGRESS_MESSAGE_ID=$(curl -s -X POST "https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage" \
            -d "chat_id=${TELEGRAM_CHAT_ID}" \
            -d "text=${full_message}" \
            -d "parse_mode=Markdown" | jq -r '.result.message_id')
    else
        curl -s -X POST "https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/editMessageText" \
            -d "chat_id=${TELEGRAM_CHAT_ID}" \
            -d "message_id=${PROGRESS_MESSAGE_ID}" \
            -d "text=${full_message}" \
            -d "parse_mode=Markdown" > /dev/null
    fi
}

# Функция для отправки обычных сообщений в Telegram
function send_telegram() {
    local message="$1"
    curl -s -X POST "https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage" \
        -d "chat_id=${TELEGRAM_CHAT_ID}" \
        -d "text=${message}" \
        -d "parse_mode=Markdown" > /dev/null
}

# Выход при ошибке
set -e

# Загрузка переменных окружения
ENV_FILE="/etc/restic-env"
if [[ ! -f "$ENV_FILE" ]]; then
    error_msg="Environment файл $ENV_FILE не найден!"
    echo "$error_msg"
    send_telegram "❌ $error_msg"
    exit 1
fi
source "$ENV_FILE"

# Пути для восстановления
RESTORE_PATH="/home/<USER>/web/sakhalife.ru/public_html/"
mkdir -p "$RESTORE_PATH"

# Разблокируем репозиторий перед началом работы
echo "Разблокировка репозитория..."
restic -r ${RESTIC_REPOSITORY} unlock --remove-all
sleep 2

# Проверяем доступность репозитория
if ! restic -r ${RESTIC_REPOSITORY} snapshots > /dev/null 2>&1; then
    # Проверяем, существует ли репозиторий
    if curl -s -I "${RESTIC_REPOSITORY}/config" | grep -q "200 OK"; then
        # Репозиторий существует, но есть проблемы с доступом
        send_telegram "⚠️ Проблема доступа к репозиторию. Пробуем разблокировать..."
        restic -r ${RESTIC_REPOSITORY} unlock --remove-all --force
        sleep 2

        # Проверяем еще раз после разблокировки
        if ! restic -r ${RESTIC_REPOSITORY} snapshots > /dev/null 2>&1; then
            error_msg="Не удалось получить доступ к репозиторию даже после разблокировки."
            echo "$error_msg"
            send_telegram "❌ $error_msg"

            # Удаляем файл PID в случае ошибки
            rm -f "$PID_FILE"
            exit 1
        fi
    else
        error_msg="Репозиторий не инициализирован или недоступен."
        echo "$error_msg"
        send_telegram "❌ $error_msg"

        # Удаляем файл PID в случае ошибки
        rm -f "$PID_FILE"
        exit 1
    fi
fi

# Список доступных снапшотов
echo -e "\nДоступные снапшоты (последние 10):"
restic -r ${RESTIC_REPOSITORY} snapshots --last 10 --json | jq -r '.[] | [.short_id, .time, .tags[]] | join(" ")'

# Получаем ID снапшота для восстановления
read -p "Введите ID снапшота для восстановления: " SNAPSHOT_ID

# Проверяем существование снапшота
if ! restic -r ${RESTIC_REPOSITORY} stats $SNAPSHOT_ID > /dev/null 2>&1; then
    error_msg="Снапшот $SNAPSHOT_ID не найден!"
    echo "$error_msg"
    send_telegram "❌ $error_msg"

    # Удаляем файл PID в случае ошибки
    rm -f "$PID_FILE"
    exit 1
fi

# Получаем информацию о снапшоте
SNAPSHOT_INFO=$(restic -r ${RESTIC_REPOSITORY} stats $SNAPSHOT_ID --json | jq -r '.total_size/1024/1024 | floor')
# Рассчитываем размер в ГБ с точностью до двух знаков после запятой
SNAPSHOT_INFO_GB=$(awk "BEGIN {printf \"%.2f\", $SNAPSHOT_INFO/1024}")
send_telegram "🔍 Запрошено восстановление
▶ Снапшот: $SNAPSHOT_ID
▶ Размер: ${SNAPSHOT_INFO} MB (${SNAPSHOT_INFO_GB} GB)
▶ Хост: ${BAKHOST}
▶ Время: $(date +'%Y-%m-%d %H:%M:%S')"

# Подтверждение восстановления
read -p "Вы уверены, что хотите восстановить снапшот $SNAPSHOT_ID в $RESTORE_PATH? [y/N] " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Восстановление отменено."
    send_telegram "⚠️ Восстановление отменено пользователем"

    # Удаляем файл PID в случае отмены
    rm -f "$PID_FILE"
    exit 0
fi

# Начинаем процесс восстановления с отображением прогресса
{
    echo -e "\nНачинаем восстановление из снапшота $SNAPSHOT_ID..."
    START_TIME=$(date +%s)
    RESTORE_START_TIME=$(date +'%Y-%m-%d %H:%M:%S')

    # Показываем начальный прогресс
    update_progress 1 "Восстановление файлов" "Процесс" "расчет..."
    sleep 2  # Добавляем задержку, чтобы пользователь увидел начальный прогресс

    echo "Запуск восстановления файлов..."
    # Сначала разблокируем репозиторий
    restic -r ${RESTIC_REPOSITORY} unlock --remove-all
    sleep 2

    # Запускаем с полным выводом в консоль
    # Используем stdbuf для отключения буферизации вывода
    stdbuf -o0 restic -r ${RESTIC_REPOSITORY} restore $SNAPSHOT_ID --target $RESTORE_PATH &
    RESTORE_PID=$!

    # Обновляем прогресс вручную
    i=1
    while kill -0 $RESTORE_PID 2>/dev/null; do
        if [ $i -lt 99 ]; then
            i=$((i+2))  # Увеличиваем шаг прогресса постепенно
        fi
        ELAPSED=$(( $(date +%s) - START_TIME ))
        REMAINING=$(estimate_remaining $ELAPSED $i)
        update_progress $i "Восстановление файлов" "Процесс" "$REMAINING"
        sleep 2  # Увеличиваем задержку между обновлениями
    done
    wait $RESTORE_PID

    # Рассчитываем время выполнения восстановления
    RESTORE_END_TIME=$(date +'%Y-%m-%d %H:%M:%S')
    RESTORE_DURATION=$(( $(date +%s) - START_TIME ))
    RESTORE_DURATION_MIN=$((RESTORE_DURATION/60))
    RESTORE_DURATION_SEC=$((RESTORE_DURATION%60))

    # Обновляем прогресс до 100%
    update_progress 100 "Восстановление файлов" "Завершен" "00:00"
    sleep 3  # Добавляем задержку, чтобы пользователь увидел завершение

    # Восстановление базы данных
    DB_DUMP="$RESTORE_PATH/mariadb.sql"
    DB_RESTORED=false
    if [[ -f "$DB_DUMP" ]]; then
        DB_SIZE=$(du -sh "$DB_DUMP" | cut -f1)
        echo -e "\nНайден дамп базы данных: $DB_DUMP ($DB_SIZE)"

        read -p "Хотите восстановить базу данных из этого дампа? [y/N] " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            echo "Восстановление базы данных..."

            # Начинаем восстановление БД
            DB_START_TIME=$(date +'%Y-%m-%d %H:%M:%S')
            START_TIME=$(date +%s)
            update_progress 1 "DB Restore" "Процесс" "расчет..."
            sleep 2  # Добавляем задержку

            # Запускаем восстановление БД в фоне
            echo "Запуск восстановления базы данных..."
            # Запускаем с полным выводом в консоль
            # Используем stdbuf для отключения буферизации вывода
            stdbuf -o0 mysql -uroot -p${MARIADB_ROOT_PASSWORD} < "$DB_DUMP" &
            DB_PID=$!

            # Обновляем прогресс вручную
            i=1
            while kill -0 $DB_PID 2>/dev/null; do
                if [ $i -lt 99 ]; then
                    i=$((i+2))  # Увеличиваем шаг прогресса постепенно
                fi
                ELAPSED=$(( $(date +%s) - START_TIME ))
                REMAINING=$(estimate_remaining $ELAPSED $i)
                update_progress $i "DB Restore" "Процесс" "$REMAINING"
                sleep 2  # Увеличиваем задержку
            done
            wait $DB_PID

            # Рассчитываем время выполнения восстановления БД
            DB_END_TIME=$(date +'%Y-%m-%d %H:%M:%S')
            DB_DURATION=$(( $(date +%s) - START_TIME ))
            DB_DURATION_MIN=$((DB_DURATION/60))
            DB_DURATION_SEC=$((DB_DURATION%60))

            # Проверяем успешность восстановления
            if [ $? -eq 0 ]; then
                update_progress 100 "DB Restore" "Завершен" "00:00"
                sleep 3  # Добавляем задержку
                DB_RESTORED=true
                send_telegram "🟢 База данных успешно восстановлена
▶ Размер дампа: $DB_SIZE
▶ Начало: $DB_START_TIME
▶ Окончание: $DB_END_TIME
▶ Длительность: ${DB_DURATION_MIN}m ${DB_DURATION_SEC}s"
            else
                update_progress 100 "DB Restore" "Ошибка" "00:00"
                send_telegram "⚠️ Ошибка восстановления БД"
            fi
        else
            send_telegram "⚠️ Восстановление БД пропущено пользователем"
        fi
    else
        echo "Дамп базы данных не найден в восстановленных файлах."
        send_telegram "ℹ️ Дамп базы данных не найден в восстановленных файлах"
    fi

    # Устанавливаем тег завершения
    echo "Устанавливаем тег 'восстановлено' для снапшота..."
    # Сначала разблокируем репозиторий, чтобы избежать ошибок блокировки
    restic -r ${RESTIC_REPOSITORY} unlock --remove-all
    sleep 2
    restic -r ${RESTIC_REPOSITORY} tag --id $SNAPSHOT_ID --set "восстановлено"
    sleep 2  # Добавляем задержку

    # Получаем информацию о снапшоте
    SNAPSHOT_TAGS=$(restic -r ${RESTIC_REPOSITORY} snapshots $SNAPSHOT_ID --json | jq -r '.[].tags | join(", ")')
    SNAPSHOT_TIME=$(restic -r ${RESTIC_REPOSITORY} snapshots $SNAPSHOT_ID --json | jq -r '.[].time')

    # Формируем сообщение о статусе БД
    DB_STATUS=""
    if [[ -f "$DB_DUMP" ]]; then
        if $DB_RESTORED; then
            DB_STATUS="\n▶ База данных: Восстановлена"
        else
            DB_STATUS="\n▶ База данных: Не восстановлена"
        fi
    fi

    # Рассчитываем общее время выполнения
    END_TIME=$(date +'%Y-%m-%d %H:%M:%S')
    TOTAL_DURATION=$(( $(date +%s) - $(date -d "$RESTORE_START_TIME" +%s) ))
    DURATION_MIN=$((TOTAL_DURATION/60))
    DURATION_SEC=$((TOTAL_DURATION%60))

    # Финальное сообщение
    send_telegram "✅ Восстановление завершено успешно
▶ Снапшот: $SNAPSHOT_ID
▶ Дата снапшота: $SNAPSHOT_TIME
▶ Путь: $RESTORE_PATH
▶ Начало: $RESTORE_START_TIME
▶ Окончание: $END_TIME
▶ Длительность: ${DURATION_MIN}m ${DURATION_SEC}s
▶ Теги: $SNAPSHOT_TAGS$DB_STATUS
▶ Статус: Тег 'восстановлено' установлен"

    # Удаляем файл PID после завершения
    rm -f "$PID_FILE"

} || {
    update_progress 0 "Процесс восстановления" "Ошибка!" "--:--"
    send_telegram "🔴 Восстановление завершилось с ошибкой!
▶ Снапшот: $SNAPSHOT_ID
▶ Время: $(date +'%Y-%m-%d %H:%M:%S')
▶ Статус: Тег 'восстановлено' не установлен"

    # Удаляем файл PID в случае ошибки
    rm -f "$PID_FILE"

    exit 1
}
