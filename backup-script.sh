#!/bin/bash

# Скрипт автоматического резервного копирования с Telegram уведомлениями
# Логика работы:
# 1. Защита от дублирования процессов (PID-файл)
# 2. Создание дампа MariaDB и backup файлов через restic
# 3. Очистка старых снапшотов (5 последних + 3 месячных)
# 4. Умные уведомления в Telegram (успешные только с 12:00-15:00 MSK, ошибки всегда)
# 5. Проверка статуса backup'ов за день и поиск незавершенных процессов
# 6. Автоматическая разблокировка репозитория и обработка ошибок

# Проверка на уже запущенный процесс
PID_FILE="/tmp/backup-script.pid"
if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if ps -p $PID > /dev/null; then
        echo "⚠️ Процесс резервного копирования уже запущен (PID: $PID)"
        exit 1
    fi
fi

# Записываем PID текущего процесса
echo $$ > "$PID_FILE"

# Имя хоста для отображения в сообщениях
BAKHOST="sakhalife.com"

# Настройки Telegram
TELEGRAM_BOT_TOKEN="**********************************************"
TELEGRAM_CHAT_ID="-1002644405949"

# Пути для резервного копирования
BACKUP_PATHS="/home/<USER>/web/sakhalife.com/public_html/"
TMP_DIR="/tmp/restic_backup"
mkdir -p "$TMP_DIR"

# Файл для отслеживания последней отправки сообщения
LAST_MESSAGE_FILE="/tmp/backup-last-message"

# Функция проверки времени отправки (с 12:00 до 15:00 MSK)
function should_send_message() {
    local current_hour=$(TZ='Europe/Moscow' date +%H)
    local current_date=$(TZ='Europe/Moscow' date +%Y-%m-%d)

    # Проверяем, что сейчас между 12:00 и 15:00 по MSK
    if [ "$current_hour" -lt "12" ] || [ "$current_hour" -gt "15" ]; then
        return 1
    fi
    
    # Проверяем, отправляли ли уже сегодня
    if [ -f "$LAST_MESSAGE_FILE" ]; then
        local last_date=$(cat "$LAST_MESSAGE_FILE")
        if [ "$last_date" = "$current_date" ]; then
            return 1  # Уже отправляли сегодня
        fi
    fi
    
    return 0  # Можно отправлять
}

# Функция проверки завершенных backup'ов за сегодня
function check_failed_backups() {
    local current_date=$(date +%Y-%m-%d)

    # Получаем все снапшоты за сегодня с подробной информацией
    local today_snapshots_json=$(restic -r ${RESTIC_REPOSITORY} snapshots --json 2>/dev/null | \
        jq --arg date "$current_date" '[.[] | select(.time | startswith($date))]')

    # Получаем краткий список снапшотов
    local today_snapshots=$(echo "$today_snapshots_json" | \
        jq -r '.[] | .short_id + " " + (.tags // [] | join(","))')

    # Формируем детальный список всех снапшотов за сегодня
    local snapshots_list=""
    if [ -n "$today_snapshots" ]; then
        snapshots_list=$(echo "$today_snapshots_json" | \
            jq -r '.[] | "🔹 " + .short_id + " ⏰ " + (.time | split("T")[1] | split(".")[0]) + " - " +
                   (if (.tags // [] | contains(["завершено"])) then "✅ завершено" else "🔄 в процессе" end)')
    fi

    if [ -z "$today_snapshots" ]; then
        # Нет снапшотов за сегодня - это проблема
        send_telegram "🚨 Нет backup'ов за сегодня ($current_date)
🖥️ Хост: ${BAKHOST}
🕐 Время проверки: $(TZ='Europe/Moscow' date +'%H:%M') MSK"
        return
    fi

    # Проверяем, есть ли завершенные backup'ы
    local completed_backups=$(echo "$today_snapshots" | grep "завершено" || true)
    local snapshot_count=$(echo "$today_snapshots" | wc -l)
    local completed_count=$(echo "$completed_backups" | grep -c "завершено" 2>/dev/null || echo "0")

    # Формируем сообщение со списком снапшотов
    local status_message=""
    if [ -z "$completed_backups" ]; then
        status_message="❌ Незавершенные backup'ы за сегодня"
    else
        status_message="� Статус backup'ов за сегодня"
    fi

    # Отправляем детальный отчет принудительно в рабочее время
    curl -s -X POST "https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage" \
        -d "chat_id=${TELEGRAM_CHAT_ID}" \
        -d "text=${status_message}
🖥️ Хост: ${BAKHOST}
📅 Дата: $current_date
📦 Всего снапшотов: $snapshot_count
✅ Завершено: $completed_count
🕐 Время проверки: $(TZ='Europe/Moscow' date +'%H:%M') MSK

📋 Список снапшотов:
$snapshots_list" \
        -d "parse_mode=Markdown" > /dev/null
}

# Функция для отправки сообщений в Telegram
function send_telegram() {
    local message="$1"
    local force="$2"  # Принудительная отправка для критических ошибок
    
    # Для критических ошибок отправляем всегда
    if [ "$force" = "force" ] || should_send_message; then
        curl -s -X POST "https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage" \
            -d "chat_id=${TELEGRAM_CHAT_ID}" \
            -d "text=${message}" \
            -d "parse_mode=Markdown" > /dev/null
        
        # Записываем дату отправки (только для обычных сообщений)
        if [ "$force" != "force" ]; then
            echo $(date +%Y-%m-%d) > "$LAST_MESSAGE_FILE"
        fi
    fi
}

# Выход при ошибке
set -e

# Загрузка переменных окружения
ENV_FILE="/etc/restic-env"
if [[ ! -f "$ENV_FILE" ]]; then
    error_msg="Environment файл $ENV_FILE не найден!"
    echo "$error_msg"
    send_telegram "🚨 *Critical Error*: Файл окружения $ENV_FILE не найден!" "force"
    exit 1
fi
source "$ENV_FILE" > /dev/null 2>&1 || {
    send_telegram "🚨 *Critical Error*: Ошибка загрузки файла окружения" "force"
    exit 1
}

# Разблокируем репозиторий перед началом работы
echo "Разблокировка репозитория..."
restic -r ${RESTIC_REPOSITORY} unlock --remove-all
sleep 2

# Проверяем доступность репозитория
if ! restic -r ${RESTIC_REPOSITORY} snapshots > /dev/null 2>&1; then
    if curl -s -I "${RESTIC_REPOSITORY}/config" | grep -q "200 OK"; then
        send_telegram "🔧 Проблема доступа к репозиторию. Пробуем разблокировать..." "force"
        restic -r ${RESTIC_REPOSITORY} unlock --remove-all --force
        sleep 2
    else
        send_telegram "🆕 Инициализация нового репозитория restic..." "force"
        echo "Инициализирую новый репозиторий..."
        restic -r ${RESTIC_REPOSITORY} init || {
            send_telegram "💥 Не удалось инициализировать репозиторий restic!" "force"
            exit 1
        }
    fi
fi

# Запоминаем время начала бэкапа
START_BACKUP_TIME=$(date +'%Y-%m-%d %H:%M:%S')

# Генерируем уникальный ID для задания
JOB_ID="job-$(uuidgen|cut -f1 -d-)"
DUMP_FILE="$TMP_DIR/mariadb.sql"

# Начинаем процесс резервного копирования
{
    echo "=== НАЧАЛО BACKUP $(date) ==="
    
    # Резервное копирование базы данных
    echo "Запуск DB Backup..."
    DB_START_TIME=$(date +'%Y-%m-%d %H:%M:%S')

    echo "Создание дампа БД..."
    mariadb-dump -uroot -p${MARIADB_ROOT_PASSWORD} --all-databases > "$DUMP_FILE"

    DUMP_SIZE=$(du -sh "$DUMP_FILE" | cut -f1)
    DB_END_TIME=$(date +'%Y-%m-%d %H:%M:%S')
    echo "DB Backup завершен. Размер дампа: $DUMP_SIZE"

    # Резервное копирование файлов
    echo "Запуск резервного копирования файлов..."
    echo "Запуск restic backup с тегом $JOB_ID..."
    restic -r ${RESTIC_REPOSITORY} backup --tag "$JOB_ID" "$BACKUP_PATHS" "$DUMP_FILE" --verbose

    restic -r ${RESTIC_REPOSITORY} unlock --remove-all
    rm -f "$DUMP_FILE"
    echo "Файлы успешно скопированы"

    # Очистка старых снапшотов
    echo "Очищаем старые снапшоты..."
    restic -r ${RESTIC_REPOSITORY} unlock --remove-all
    sleep 2
    
    echo "Команда: restic forget --keep-last 5 --keep-monthly 3"
    restic -r ${RESTIC_REPOSITORY} forget --keep-last 5 --keep-monthly 3 --verbose
    
    echo "Команда: restic prune"
    restic -r ${RESTIC_REPOSITORY} prune --verbose

    # Получаем статистику
    STATS_MB=$(restic -r ${RESTIC_REPOSITORY} stats --mode raw-data --json | jq -r '.total_size/1024/1024 | floor')
    STATS_GB=$(awk "BEGIN {printf \"%.2f\", $STATS_MB/1024}")

    echo "Устанавливаем тег 'завершено' для снапшота..."
    restic -r ${RESTIC_REPOSITORY} unlock --remove-all
    sleep 2
    restic -r ${RESTIC_REPOSITORY} tag --tag "$JOB_ID" --set "завершено"

    # Рассчитываем общее время выполнения
    END_TIME=$(date +'%Y-%m-%d %H:%M:%S')
    TOTAL_DURATION=$(( $(date +%s) - $(date -d "$START_BACKUP_TIME" +%s) ))
    DURATION_MIN=$((TOTAL_DURATION/60))
    DURATION_SEC=$((TOTAL_DURATION%60))

    echo "=== BACKUP ЗАВЕРШЕН УСПЕШНО $(date) ==="
    echo "Общее время: ${DURATION_MIN}минут ${DURATION_SEC}секунд"
    echo "Размер репозитория: ${STATS_MB} MB (${STATS_GB} GB)"

    # Отправляем сообщение только в рабочее время (12:00-15:00 MSK)
    current_hour=$(TZ='Europe/Moscow' date +%H)
    echo "Текущее время MSK: $(TZ='Europe/Moscow' date +'%H:%M'), час: $current_hour"
    if [ "$current_hour" -ge "12" ] && [ "$current_hour" -le "15" ]; then
        # Детальное сообщение с общей статистикой (отправляем принудительно)
        curl -s -X POST "https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage" \
            -d "chat_id=${TELEGRAM_CHAT_ID}" \
            -d "text=🎉 Backup завершен успешно
🖥️ Хост: ${BAKHOST}
💾 Размер: ${STATS_MB} MB (${STATS_GB} GB)
⏱️ Длительность: ${DURATION_MIN}m ${DURATION_SEC}s
🏷️ Тег: $JOB_ID
✅ Статус: завершено
🕐 Время: $(TZ='Europe/Moscow' date +'%H:%M') MSK" \
            -d "parse_mode=Markdown" > /dev/null
    else
        # В нерабочее время сообщения не отправляем (только при ошибках)
        echo "Backup завершен успешно, но сообщение не отправлено (нерабочее время)"
    fi

    rm -f "$PID_FILE"

} || {
    echo "=== ОШИБКА BACKUP $(date) ==="
    
    # Ошибки отправляем всегда принудительно
    send_telegram "� Backup завершился с ошибкой!
🖥️ Хост: ${BAKHOST}
🕐 Время: $(date +'%Y-%m-%d %H:%M:%S')" "force"

    rm -f "$PID_FILE"
    exit 1
}

# Проверяем неудачные backup'ы только в период с 12 до 15 MSK
current_hour=$(TZ='Europe/Moscow' date +%H)
echo "Финальная проверка времени MSK: $(TZ='Europe/Moscow' date +'%H:%M'), час: $current_hour"
if [ "$current_hour" -ge "12" ] && [ "$current_hour" -le "15" ]; then
    echo "Проверка неудачных backup'ов за сегодня ( время MSK: $(TZ='Europe/Moscow' date +'%H:%M'))..."
    check_failed_backups
else
    echo "Вне рабочего времени, детальная проверка не выполняется"
fi


